// Alpine UI Tabs Implementation
// This provides a wrapper around Alpine UI tabs with persistence and custom styling

export default function (tabs = null, count_name = "count") {
  return {
    tabs: tabs,
    // For backward compatibility, we'll maintain the activeTab property
    // but Alpine UI will handle the actual tab state internally
    activeTab: this.$persist(0).as(count_name),
    activeClasses: "border-zeiss-500 text-zeiss-600",
    inactiveClasses: "text-gray-500 hover:text-gray-700 hover:border-gray-300",

    // Initialize Alpine UI tabs with persistence
    init() {
      // Set up Alpine UI tabs with initial state from persistence
      this.$nextTick(() => {
        if (this.tabs && this.activeTab !== undefined) {
          // Find the tab button and trigger click to set initial state
          const tabButtons = this.$el.querySelectorAll('[x-tabs\\:tab]');
          if (tabButtons[this.activeTab]) {
            tabButtons[this.activeTab].click();
          }
        }
      });
    },

    // Handle tab change for persistence
    onTabChange(index) {
      this.activeTab = index;
    },

    // Get classes for tab styling (for backward compatibility)
    getTabClasses(isSelected) {
      return isSelected ? this.activeClasses : this.inactiveClasses;
    }
  };
}

export default function (Alpine) {
  // Mobile-specific functionality for reports
  Alpine.data('mobileReports', () => ({
    isMobile: false,
    isTablet: false,
    orientation: 'portrait',
    sidebarOpen: false,
    activeTab: 'overview',
    touchStartX: 0,
    touchStartY: 0,

    init() {
      this.checkDevice();
      this.setupEventListeners();
      this.optimizeForMobile();
    },

    checkDevice() {
      this.isMobile = window.innerWidth < 768;
      this.isTablet = window.innerWidth >= 768 && window.innerWidth < 1024;
      this.orientation = window.innerHeight > window.innerWidth ? 'portrait' : 'landscape';
    },

    setupEventListeners() {
      window.addEventListener('resize', () => {
        this.checkDevice();
        this.optimizeForMobile();
      });

      window.addEventListener('orientationchange', () => {
        setTimeout(() => {
          this.checkDevice();
          this.optimizeForMobile();
          this.refreshCharts();
        }, 100);
      });

      // Touch event listeners for swipe gestures
      document.addEventListener('touchstart', (e) => {
        this.touchStartX = e.touches[0].clientX;
        this.touchStartY = e.touches[0].clientY;
      });

      document.addEventListener('touchend', (e) => {
        if (!this.touchStartX || !this.touchStartY) return;

        const touchEndX = e.changedTouches[0].clientX;
        const touchEndY = e.changedTouches[0].clientY;

        const deltaX = this.touchStartX - touchEndX;
        const deltaY = this.touchStartY - touchEndY;

        // Horizontal swipe detection
        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
          if (deltaX > 0) {
            this.handleSwipeLeft();
          } else {
            this.handleSwipeRight();
          }
        }

        this.touchStartX = 0;
        this.touchStartY = 0;
      });
    },

    optimizeForMobile() {
      if (this.isMobile) {
        this.enableMobileOptimizations();
      } else {
        this.disableMobileOptimizations();
      }
    },

    enableMobileOptimizations() {
      // Adjust chart sizes for mobile
      this.adjustChartSizes();

      // Enable touch-friendly interactions
      this.enableTouchInteractions();

      // Optimize table layouts
      this.optimizeTableLayouts();

      // Adjust font sizes
      this.adjustFontSizes();
    },

    disableMobileOptimizations() {
      // Reset to desktop optimizations
      this.resetChartSizes();
      this.resetTableLayouts();
      this.resetFontSizes();
    },

    adjustChartSizes() {
      const charts = document.querySelectorAll('[data-chart-type]');
      charts.forEach(chart => {
        const container = chart.closest('[style*="height"]');
        if (container) {
          if (this.isMobile) {
            container.style.height = '250px';
          } else if (this.isTablet) {
            container.style.height = '300px';
          }
        }
      });
    },

    resetChartSizes() {
      const charts = document.querySelectorAll('[data-chart-type]');
      charts.forEach(chart => {
        const container = chart.closest('[style*="height"]');
        if (container) {
          container.style.height = '400px';
        }
      });
    },

    enableTouchInteractions() {
      // Add touch-friendly button sizes
      const buttons = document.querySelectorAll('button, .btn');
      buttons.forEach(button => {
        if (this.isMobile) {
          button.style.minHeight = '44px';
          button.style.minWidth = '44px';
        }
      });

      // Enable touch scrolling for tables
      const tables = document.querySelectorAll('table');
      tables.forEach(table => {
        const wrapper = table.closest('.overflow-x-auto');
        if (wrapper && this.isMobile) {
          wrapper.style.webkitOverflowScrolling = 'touch';
        }
      });
    },

    optimizeTableLayouts() {
      const tables = document.querySelectorAll('table');
      tables.forEach(table => {
        if (this.isMobile) {
          // Convert tables to card layout on mobile
          this.convertTableToCards(table);
        } else {
          // Restore table layout
          this.restoreTableLayout(table);
        }
      });
    },

    convertTableToCards(table) {
      if (table.dataset.mobileConverted === 'true') return;

      const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent.trim());
      const rows = table.querySelectorAll('tbody tr');

      const cardContainer = document.createElement('div');
      cardContainer.className = 'space-y-4 mobile-cards';

      rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        const card = document.createElement('div');
        card.className = 'bg-white border border-gray-200 rounded-lg p-4 shadow-sm';

        cells.forEach((cell, index) => {
          if (headers[index]) {
            const field = document.createElement('div');
            field.className = 'flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0';
            field.innerHTML = `
              <span class="text-sm font-medium text-gray-600">${headers[index]}</span>
              <span class="text-sm text-gray-900">${cell.innerHTML}</span>
            `;
            card.appendChild(field);
          }
        });

        cardContainer.appendChild(card);
      });

      table.style.display = 'none';
      table.dataset.mobileConverted = 'true';
      table.parentNode.insertBefore(cardContainer, table.nextSibling);
    },

    restoreTableLayout(table) {
      if (table.dataset.mobileConverted !== 'true') return;

      const cardContainer = table.parentNode.querySelector('.mobile-cards');
      if (cardContainer) {
        cardContainer.remove();
      }

      table.style.display = '';
      table.dataset.mobileConverted = 'false';
    },

    adjustFontSizes() {
      if (this.isMobile) {
        document.documentElement.style.fontSize = '14px';
      } else {
        document.documentElement.style.fontSize = '16px';
      }
    },

    resetFontSizes() {
      document.documentElement.style.fontSize = '16px';
    },

    handleSwipeLeft() {
      // Navigate to next tab or close sidebar
      if (this.sidebarOpen) {
        this.sidebarOpen = false;
      } else {
        this.nextTab();
      }
    },

    handleSwipeRight() {
      // Navigate to previous tab or open sidebar
      if (!this.sidebarOpen) {
        this.sidebarOpen = true;
      } else {
        this.previousTab();
      }
    },

    nextTab() {
      const tabs = ['overview', 'analytics', 'performance', 'financial'];
      const currentIndex = tabs.indexOf(this.activeTab);
      const nextIndex = (currentIndex + 1) % tabs.length;
      this.activeTab = tabs[nextIndex];
      this.switchToTab(this.activeTab);
    },

    previousTab() {
      const tabs = ['overview', 'analytics', 'performance', 'financial'];
      const currentIndex = tabs.indexOf(this.activeTab);
      const previousIndex = currentIndex === 0 ? tabs.length - 1 : currentIndex - 1;
      this.activeTab = tabs[previousIndex];
      this.switchToTab(this.activeTab);
    },

    switchToTab(tabName, tabIndex = null) {
      this.activeTab = tabName;

      // If we have Alpine UI tabs, let Alpine handle the tab switching
      if (tabIndex !== null) {
        const tabButtons = document.querySelectorAll('[x-tabs\\:tab]');
        if (tabButtons[tabIndex] && !tabButtons[tabIndex].classList.contains('active')) {
          // Let Alpine UI handle the tab switching
          return;
        }
      }

      // Fallback to manual tab switching for backward compatibility
      // Hide all tab content
      document.querySelectorAll('[data-tab-content]').forEach(content => {
        content.style.display = 'none';
        content.classList.add('hidden');
        content.classList.remove('block');
      });

      // Show selected tab content
      const targetContent = document.querySelector(`[data-tab-content="${tabName}"]`);
      if (targetContent) {
        targetContent.style.display = 'block';
        targetContent.classList.remove('hidden');
        targetContent.classList.add('block');
      }

      // Update tab indicators (for non-Alpine UI tabs)
      document.querySelectorAll('[data-tab]').forEach(tab => {
        tab.classList.remove('active', 'border-zeiss-500', 'text-zeiss-600', 'bg-zeiss-50');
        tab.classList.add('text-gray-500', 'hover:text-gray-700');
      });

      const activeTabElement = document.querySelector(`[data-tab="${tabName}"]`);
      if (activeTabElement) {
        activeTabElement.classList.add('active', 'border-zeiss-500', 'text-zeiss-600', 'bg-zeiss-50');
        activeTabElement.classList.remove('text-gray-500', 'hover:text-gray-700');
      }
    },

    refreshCharts() {
      // Refresh all charts after orientation change
      if (window.chartInstances) {
        Object.values(window.chartInstances).forEach(chart => {
          if (chart && typeof chart.resize === 'function') {
            chart.resize();
          }
        });
      }
    },

    toggleMobileMenu() {
      this.sidebarOpen = !this.sidebarOpen;
    },

    closeMobileMenu() {
      this.sidebarOpen = false;
    },

    // Utility methods for mobile interactions
    vibrate(pattern = [100]) {
      if ('vibrate' in navigator) {
        navigator.vibrate(pattern);
      }
    },

    showToast(message, type = 'info') {
      const toast = document.createElement('div');
      toast.className = `fixed bottom-4 left-4 right-4 p-4 rounded-lg shadow-lg z-50 ${this.getToastClasses(type)}`;
      toast.textContent = message;

      document.body.appendChild(toast);

      // Animate in
      setTimeout(() => {
        toast.style.transform = 'translateY(0)';
        toast.style.opacity = '1';
      }, 10);

      // Remove after 3 seconds
      setTimeout(() => {
        toast.style.transform = 'translateY(100%)';
        toast.style.opacity = '0';
        setTimeout(() => {
          if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
          }
        }, 300);
      }, 3000);
    },

    getToastClasses(type) {
      const baseClasses = 'transform translate-y-full opacity-0 transition-all duration-300';
      switch (type) {
        case 'success':
          return `${baseClasses} bg-green-500 text-white`;
        case 'error':
          return `${baseClasses} bg-red-500 text-white`;
        case 'warning':
          return `${baseClasses} bg-yellow-500 text-white`;
        default:
          return `${baseClasses} bg-blue-500 text-white`;
      }
    }
  }));

  // Global mobile utility functions
  window.mobileReports = {
    showMobileFilters() {
      const filtersModal = document.getElementById('mobile-filters-modal');
      if (filtersModal) {
        filtersModal.classList.remove('hidden');
      }
    },

    hideMobileFilters() {
      const filtersModal = document.getElementById('mobile-filters-modal');
      if (filtersModal) {
        filtersModal.classList.add('hidden');
      }
    },

    exportForMobile(format) {
      // Show loading indicator
      const loadingToast = document.createElement('div');
      loadingToast.className = 'fixed bottom-4 left-4 right-4 p-4 rounded-lg shadow-lg z-50 bg-blue-500 text-white';
      loadingToast.innerHTML = `
        <div class="flex items-center">
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
          Preparing ${format.toUpperCase()} export...
        </div>
      `;
      document.body.appendChild(loadingToast);

      // Remove loading toast after export starts
      setTimeout(() => {
        if (loadingToast.parentNode) {
          loadingToast.parentNode.removeChild(loadingToast);
        }
      }, 2000);
    }
  };
}

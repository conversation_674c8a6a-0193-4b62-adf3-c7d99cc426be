<%= render Page::Component.new do |page| %>
  <% page.with_header title: "Welcome #{current_user.name.titleize}" %>

  <!-- Personal Performance Overview -->
  <%= render Dashboard::SectionComponent.new(title: "Personal Performance", subtitle: "Your key metrics at a glance") do %>
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <% monthly_sales = current_user.sales.where(created_at: Date.current.beginning_of_month..Date.current.end_of_month).count %>
      <% last_month_sales = current_user.sales.where(created_at: 1.month.ago.beginning_of_month..1.month.ago.end_of_month).count %>
      <% change = last_month_sales > 0 ? ((monthly_sales - last_month_sales) / last_month_sales.to_f * 100).round(1) : 0 %>
      <% change_text = change != 0 ? "#{change >= 0 ? '+' : ''}#{change}%" : nil %>
      <% change_type = change > 0 ? "positive" : (change < 0 ? "negative" : "neutral") %>

      <%= render Dashboard::StatCardComponent.new(
        title: "Available Points",
        value: current_user.wallet&.balance || 0,
        icon_name: "coins",
        color: "green",
        link_to: shop_path
      ) %>

      <%= render Dashboard::StatCardComponent.new(
        title: "Sales This Month",
        value: monthly_sales,
        icon_name: "trending-up",
        color: "blue",
        change: change_text,
        change_type: change_type,
        link_to: sales_path
      ) %>

      <%= render Dashboard::StatCardComponent.new(
        title: "Points Earned",
        subtitle: "This month",
        value: current_user.sales.approved.where(created_at: Date.current.beginning_of_month..Date.current.end_of_month).sum(:points),
        icon_name: "star",
        color: "yellow"
      ) %>

      <%= render Dashboard::StatCardComponent.new(
        title: "Pending Sales",
        value: current_user.sales.pending.count,
        icon_name: "clock",
        color: "orange",
        link_to: sales_path + "?status=pending"
      ) %>
    </div>
  <% end %>

  <!-- Quick Actions -->
  <%= render Dashboard::SectionComponent.new(title: "Quick Actions", subtitle: "Common tasks and shortcuts") do %>
    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
      <%= render Dashboard::QuickActionComponent.new(
        title: "New Sale",
        description: "Record a new product sale",
        icon: "plus",
        url: new_sale_path,
        color: "green"
      ) %>

      <%= render Dashboard::QuickActionComponent.new(
        title: "Point Shop",
        description: "Browse and redeem rewards",
        icon: "shopping-bag",
        url: shop_path,
        color: "blue"
      ) %>

      <% pending_count = current_user.sales.pending.count %>
      <%= render Dashboard::QuickActionComponent.new(
        title: "View Sales",
        description: "Review your sales history",
        icon: "chart-bar",
        url: sales_path,
        color: "purple",
        badge: pending_count > 0 ? "#{pending_count} pending" : nil,
        badge_color: "yellow"
      ) %>

      <%= render Dashboard::QuickActionComponent.new(
        title: "My Orders",
        description: "Track your point redemptions",
        icon: "package",
        url: orders_path,
        color: "indigo"
      ) %>
    </div>
  <% end %>

  <!-- Recent Activity -->
  <div class="space-y-8">
    <div>
      <div class="pb-5 mb-4 border-b border-gray-200 sm:flex sm:items-center sm:justify-between">
        <h3 class="text-lg font-medium leading-6 text-gray-900">Recent Sales</h3>
        <div class="flex mt-3 sm:mt-0 sm:ml-4">
          <%= link_to "View All", sales_path, class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-xs text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
        </div>
      </div>
      <%= turbo_frame_tag "sales_results", src: sales_path(limit: 5) %>
    </div>
    <div>
      <div class="pb-5 mb-4 border-b border-gray-200 sm:flex sm:items-center sm:justify-between">
        <h3 class="text-lg font-medium leading-6 text-gray-900">Recent Orders</h3>
        <div class="flex mt-3 sm:mt-0 sm:ml-4">
          <%= link_to "View All", orders_path, class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-xs text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
        </div>
      </div>
      <%= turbo_frame_tag "orders_results", src: orders_path(limit: 5) %>
    </div>

    <!-- Performance Insights -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Your Performance</h3>
        <div class="grid grid-cols-1 gap-5 sm:grid-cols-2">
          <!-- Monthly Progress Chart -->
          <div>
            <h4 class="text-sm font-medium text-gray-500 mb-2">Sales Trend (Last 6 Months)</h4>
            <div class="h-32 bg-gray-50 rounded flex items-center justify-center">
              <p class="text-sm text-gray-500">Chart will be implemented with real data</p>
            </div>
          </div>

          <!-- Top Products -->
          <div>
            <h4 class="text-sm font-medium text-gray-500 mb-2">Your Top Products</h4>
            <div class="space-y-2">
              <% current_user.sales.approved.joins(:product).group('products.name').limit(3).count.each do |product_name, count| %>
                <div class="flex justify-between items-center">
                  <span class="text-sm text-gray-900"><%= product_name %></span>
                  <span class="text-sm font-medium text-gray-500"><%= count %> sales</span>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Goals and Achievements -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Monthly Goals</h3>
        <% monthly_sales = current_user.sales.where(created_at: Date.current.beginning_of_month..Date.current.end_of_month).count %>
        <% monthly_goal = 10 %> <!-- This could be configurable per user -->
        <% progress_percentage = [(monthly_sales.to_f / monthly_goal * 100).round, 100].min %>

        <div class="mb-4">
          <div class="flex justify-between text-sm font-medium text-gray-900 mb-1">
            <span>Sales Goal Progress</span>
            <span><%= monthly_sales %>/<%= monthly_goal %></span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="bg-zeiss-600 h-2 rounded-full" style="width: <%= progress_percentage %>%"></div>
          </div>
          <p class="text-xs text-gray-500 mt-1"><%= progress_percentage %>% complete</p>
        </div>

        <% if progress_percentage >= 100 %>
          <div class="rounded-md bg-green-50 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <%= icon name: "check-circle", class: "h-5 w-5 text-green-400" %>
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-green-800">Congratulations!</p>
                <p class="text-sm text-green-700">You've reached your monthly sales goal!</p>
              </div>
            </div>
          </div>
        <% elsif progress_percentage >= 75 %>
          <div class="rounded-md bg-yellow-50 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <%= icon name: "exclamation-triangle", class: "h-5 w-5 text-yellow-400" %>
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-yellow-800">Almost there!</p>
                <p class="text-sm text-yellow-700">You need <%= monthly_goal - monthly_sales %> more sales to reach your goal.</p>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
<% end %>

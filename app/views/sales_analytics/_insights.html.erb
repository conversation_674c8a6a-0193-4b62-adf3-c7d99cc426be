<% if insights.any? %>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900">AI-Powered Insights</h3>
      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
        <%= pluralize(insights.count, 'insight') %>
      </span>
    </div>

    <div class="space-y-4">
      <% insights.each do |insight| %>
        <div class="flex items-start p-4 rounded-lg <%= insight_background_class(insight[:type]) %>">
          <div class="flex-shrink-0">
            <%= insight_icon(insight[:type]) %>
          </div>
          <div class="ml-3 flex-1">
            <h4 class="text-sm font-medium <%= insight_text_class(insight[:type]) %>">
              <%= insight[:title] %>
            </h4>
            <p class="text-sm <%= insight_description_class(insight[:type]) %> mt-1">
              <%= insight[:description] %>
            </p>
            <div class="mt-2 flex items-center">
              <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium <%= insight_impact_class(insight[:impact]) %>">
                <%= insight[:impact].humanize %> Impact
              </span>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
<% else %>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="text-center">
      <%= icon name: "lightbulb", class: "h-12 w-12 text-gray-400 mx-auto mb-4" %>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No Insights Available</h3>
      <p class="text-sm text-gray-600">
        Insights will appear here as we analyze your sales data.
        Try selecting a different date range or check back later.
      </p>
    </div>
  </div>
<% end %>

<%
  def insight_background_class(type)
    case type
    when 'positive'
      'bg-green-50 border border-green-200'
    when 'warning'
      'bg-yellow-50 border border-yellow-200'
    when 'negative'
      'bg-red-50 border border-red-200'
    else
      'bg-blue-50 border border-blue-200'
    end
  end

  def insight_text_class(type)
    case type
    when 'positive'
      'text-green-900'
    when 'warning'
      'text-yellow-900'
    when 'negative'
      'text-red-900'
    else
      'text-blue-900'
    end
  end

  def insight_description_class(type)
    case type
    when 'positive'
      'text-green-700'
    when 'warning'
      'text-yellow-700'
    when 'negative'
      'text-red-700'
    else
      'text-blue-700'
    end
  end

  def insight_impact_class(impact)
    case impact
    when 'high'
      'bg-red-100 text-red-800'
    when 'medium'
      'bg-yellow-100 text-yellow-800'
    when 'low'
      'bg-green-100 text-green-800'
    else
      'bg-gray-100 text-gray-800'
    end
  end

  def insight_icon(type)
    case type
    when 'positive'
      icon name: "chart-line-up", class: "h-5 w-5 text-green-600"
    when 'warning'
      icon name: "triangle-exclamation", class: "h-5 w-5 text-yellow-600"
    when 'negative'
      icon name: "chart-line-up", class: "h-5 w-5 text-red-600", style: "transform: scaleY(-1);"
    else
      icon name: "circle-info", class: "h-5 w-5 text-blue-600"
    end
  end
%>

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
  <!-- Sales Metric -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-sm font-medium text-gray-600">Sales Today</p>
        <p class="text-2xl font-bold text-gray-900"><%= metrics[:sales][:current] %></p>
        <% if metrics[:sales][:change] != 0 %>
          <p class="text-sm <%= metrics[:sales][:trend] == 'up' ? 'text-green-600' : metrics[:sales][:trend] == 'down' ? 'text-red-600' : 'text-gray-500' %>">
            <%= metrics[:sales][:trend] == 'up' ? '↗' : metrics[:sales][:trend] == 'down' ? '↘' : '→' %>
            <%= metrics[:sales][:change].abs %>% vs yesterday
          </p>
        <% end %>
      </div>
      <div class="p-3 bg-blue-100 rounded-full">
        <%= icon name: "chart-line-up", class: "h-6 w-6 text-blue-600" %>
      </div>
    </div>
  </div>

  <!-- Orders Metric -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-sm font-medium text-gray-600">Orders Today</p>
        <p class="text-2xl font-bold text-gray-900"><%= metrics[:orders][:current] %></p>
        <% if metrics[:orders][:change] != 0 %>
          <p class="text-sm <%= metrics[:orders][:trend] == 'up' ? 'text-green-600' : metrics[:orders][:trend] == 'down' ? 'text-red-600' : 'text-gray-500' %>">
            <%= metrics[:orders][:trend] == 'up' ? '↗' : metrics[:orders][:trend] == 'down' ? '↘' : '→' %>
            <%= metrics[:orders][:change].abs %>% vs yesterday
          </p>
        <% end %>
      </div>
      <div class="p-3 bg-green-100 rounded-full">
        <%= icon name: "cart-shopping", class: "h-6 w-6 text-green-600" %>
      </div>
    </div>
  </div>

  <!-- Points Metric -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-sm font-medium text-gray-600">Points Earned</p>
        <p class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(metrics[:points][:current]) %></p>
        <% if metrics[:points][:change] != 0 %>
          <p class="text-sm <%= metrics[:points][:trend] == 'up' ? 'text-green-600' : metrics[:points][:trend] == 'down' ? 'text-red-600' : 'text-gray-500' %>">
            <%= metrics[:points][:trend] == 'up' ? '↗' : metrics[:points][:trend] == 'down' ? '↘' : '→' %>
            <%= metrics[:points][:change].abs %>% vs yesterday
          </p>
        <% end %>
      </div>
      <div class="p-3 bg-yellow-100 rounded-full">
        <%= icon name: "star", class: "h-6 w-6 text-yellow-600" %>
      </div>
    </div>
  </div>

  <!-- Active Users / Pending Approvals -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="flex items-center justify-between">
      <div>
        <% if current_user.admin? || current_user.super_admin? %>
          <p class="text-sm font-medium text-gray-600">Active Users</p>
          <p class="text-2xl font-bold text-gray-900"><%= metrics[:active_users] %></p>
          <p class="text-sm text-gray-500">Last hour</p>
        <% else %>
          <p class="text-sm font-medium text-gray-600">Pending Approvals</p>
          <p class="text-2xl font-bold text-gray-900"><%= metrics[:pending_approvals] %></p>
          <p class="text-sm text-gray-500">Awaiting review</p>
        <% end %>
      </div>
      <div class="p-3 bg-purple-100 rounded-full">
        <% if current_user.admin? || current_user.super_admin? %>
          <%= icon name: "users", class: "h-6 w-6 text-purple-600" %>
        <% else %>
          <%= icon name: "clock", class: "h-6 w-6 text-purple-600" %>
        <% end %>
      </div>
    </div>
  </div>
</div>

<!-- Last Updated Indicator -->
<div class="mt-4 text-center">
  <p class="text-xs text-gray-500">
    Last updated: <%= metrics[:last_updated] %>
    <span class="inline-flex items-center ml-2">
      <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
      <span class="ml-1">Live</span>
    </span>
  </p>
</div>

<div class="bg-white rounded-lg shadow-sm border border-gray-200">
  <div class="px-6 py-4 border-b border-gray-200">
    <h3 class="text-lg font-semibold text-gray-900">System Health</h3>
    <p class="text-sm text-gray-600 mt-1">Real-time system monitoring</p>
  </div>

  <div class="p-6">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Database Health -->
      <div class="text-center">
        <div class="mx-auto w-12 h-12 rounded-full flex items-center justify-center mb-3 <%= health[:database][:status] == 'healthy' ? 'bg-green-100' : 'bg-red-100' %>">
          <%= icon name: "database", class: "h-6 w-6 #{health[:database][:status] == 'healthy' ? 'text-green-600' : 'text-red-600'}" %>
        </div>
        <h4 class="text-sm font-medium text-gray-900">Database</h4>
        <p class="text-xs text-gray-600 mt-1">
          <%= health[:database][:status].humanize %>
        </p>
        <p class="text-xs text-gray-500">
          <%= health[:database][:response_time] %>ms
        </p>
      </div>

      <!-- Cache Health -->
      <div class="text-center">
        <div class="mx-auto w-12 h-12 rounded-full flex items-center justify-center mb-3 <%= health[:cache][:status] == 'healthy' ? 'bg-green-100' : 'bg-red-100' %>">
          <%= icon name: "bolt", class: "h-6 w-6 #{health[:cache][:status] == 'healthy' ? 'text-green-600' : 'text-red-600'}" %>
        </div>
        <h4 class="text-sm font-medium text-gray-900">Cache</h4>
        <p class="text-xs text-gray-600 mt-1">
          <%= health[:cache][:status].humanize %>
        </p>
        <p class="text-xs text-gray-500">
          <%= health[:cache][:hit_rate] %>% hit rate
        </p>
      </div>

      <!-- Queue Health -->
      <div class="text-center">
        <div class="mx-auto w-12 h-12 rounded-full flex items-center justify-center mb-3 <%= health[:queue][:status] == 'healthy' ? 'bg-green-100' : health[:queue][:status] == 'warning' ? 'bg-yellow-100' : 'bg-red-100' %>">
          <%= icon name: "bars", class: "h-6 w-6 #{health[:queue][:status] == 'healthy' ? 'text-green-600' : health[:queue][:status] == 'warning' ? 'text-yellow-600' : 'text-red-600'}" %>
        </div>
        <h4 class="text-sm font-medium text-gray-900">Queue</h4>
        <p class="text-xs text-gray-600 mt-1">
          <%= health[:queue][:status].humanize %>
        </p>
        <p class="text-xs text-gray-500">
          <%= health[:queue][:pending_jobs] %> pending
        </p>
      </div>

      <!-- Memory Usage -->
      <div class="text-center">
        <div class="mx-auto w-12 h-12 rounded-full flex items-center justify-center mb-3 <%= health[:memory][:status] == 'healthy' ? 'bg-green-100' : health[:memory][:status] == 'warning' ? 'bg-yellow-100' : 'bg-red-100' %>">
          <%= icon name: "microchip", class: "h-6 w-6 #{health[:memory][:status] == 'healthy' ? 'text-green-600' : health[:memory][:status] == 'warning' ? 'text-yellow-600' : 'text-red-600'}" %>
        </div>
        <h4 class="text-sm font-medium text-gray-900">Memory</h4>
        <p class="text-xs text-gray-600 mt-1">
          <%= health[:memory][:status].humanize %>
        </p>
        <p class="text-xs text-gray-500">
          <%= health[:memory][:usage] %>% used
        </p>
      </div>
    </div>

    <!-- Overall System Status -->
    <div class="mt-6 pt-6 border-t border-gray-200">
      <% overall_status = [health[:database][:status], health[:cache][:status], health[:queue][:status], health[:memory][:status]] %>
      <% if overall_status.all? { |status| status == 'healthy' } %>
        <div class="flex items-center justify-center p-4 bg-green-50 border border-green-200 rounded-lg">
          <%= icon name: "circle-check", class: "h-5 w-5 text-green-600 mr-2" %>
          <span class="text-sm font-medium text-green-900">All systems operational</span>
        </div>
      <% elsif overall_status.any? { |status| status == 'error' } %>
        <div class="flex items-center justify-center p-4 bg-red-50 border border-red-200 rounded-lg">
          <%= icon name: "circle-exclamation", class: "h-5 w-5 text-red-600 mr-2" %>
          <span class="text-sm font-medium text-red-900">System issues detected</span>
        </div>
      <% else %>
        <div class="flex items-center justify-center p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <%= icon name: "triangle-exclamation", class: "h-5 w-5 text-yellow-600 mr-2" %>
          <span class="text-sm font-medium text-yellow-900">Some systems need attention</span>
        </div>
      <% end %>
    </div>
  </div>
</div>

class Charts::LineChartComponent < Charts::BaseComponent
  option :smooth, type: Types::Bool, default: proc { true }
  option :fill, type: Types::Bool, default: proc { false }
  option :show_points, type: Types::Bool, default: proc { true }
  option :colors, type: Types::Array, default: proc { ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd"] }
  option :x_axis_label, type: Types::String.optional, optional: true
  option :y_axis_label, type: Types::String.optional, optional: true
  option :show_grid, type: Types::Bool, default: proc { true }
  option :stacked, type: Types::Bool, default: proc { false }

  # Override options to include line chart specific options
  def options
    super.merge(
      smooth: smooth,
      fill: fill,
      show_points: show_points,
      colors: colors,
      x_axis_label: x_axis_label,
      y_axis_label: y_axis_label,
      show_grid: show_grid,
      stacked: stacked
    )
  end

  private

  def chart_type
    "line"
  end

  def chart_options
    super.deep_merge({
      scales: {
        x: {
          display: true,
          title: {
            display: x_axis_label.present?,
            text: x_axis_label
          },
          grid: {
            display: show_grid
          }
        },
        y: {
          display: true,
          title: {
            display: y_axis_label.present?,
            text: y_axis_label
          },
          grid: {
            display: show_grid
          },
          stacked: stacked
        }
      },
      elements: {
        line: {
          tension: smooth ? 0.4 : 0,
          fill: fill
        },
        point: {
          radius: show_points ? 4 : 0,
          hoverRadius: 6
        }
      },
      interaction: {
        intersect: false,
        mode: "index"
      }
    })
  end

  def formatted_data
    case data
    when Hash
      # Handle hash data with labels and datasets
      {
        labels: data[:labels] || data["labels"] || [],
        datasets: format_datasets(data[:datasets] || data["datasets"] || [])
      }
    when Array
      # Handle simple array data
      if data.first.is_a?(Hash) && data.first.key?(:x)
        # Time series data
        {
          datasets: [{
            label: title || "Data",
            data: data,
            borderColor: colors.first,
            backgroundColor: fill ? "#{colors.first}20" : "transparent",
            tension: smooth ? 0.4 : 0,
            fill: fill
          }]
        }
      else
        # Simple label-value pairs
        labels = data.map.with_index { |_, i| "Point #{i + 1}" }
        {
          labels: labels,
          datasets: [{
            label: title || "Data",
            data: data,
            borderColor: colors.first,
            backgroundColor: fill ? "#{colors.first}20" : "transparent",
            tension: smooth ? 0.4 : 0,
            fill: fill
          }]
        }
      end
    else
      {labels: [], datasets: []}
    end
  end

  def format_datasets(datasets)
    datasets.map.with_index do |dataset, index|
      color = colors[index % colors.length]
      {
        label: dataset[:label] || dataset["label"] || "Dataset #{index + 1}",
        data: dataset[:data] || dataset["data"] || [],
        borderColor: color,
        backgroundColor: fill ? "#{color}20" : "transparent",
        tension: smooth ? 0.4 : 0,
        fill: fill,
        pointRadius: show_points ? 4 : 0,
        pointHoverRadius: 6
      }
    end
  end
end

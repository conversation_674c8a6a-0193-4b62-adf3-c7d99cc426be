class Charts::BarChartComponent < Charts::BaseComponent
  option :horizontal, type: Types::Bool, default: proc { false }
  option :stacked, type: Types::Bool, default: proc { false }
  option :colors, type: Types::Array, default: proc { ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd"] }
  option :x_axis_label, type: Types::String.optional, optional: true
  option :y_axis_label, type: Types::String.optional, optional: true
  option :show_grid, type: Types::Bool, default: proc { true }
  option :show_values, type: Types::Bool, default: proc { false }

  # Override options to include bar chart specific options
  def options
    super.merge(
      horizontal: horizontal,
      stacked: stacked,
      colors: colors,
      x_axis_label: x_axis_label,
      y_axis_label: y_axis_label,
      show_grid: show_grid,
      show_values: show_values
    )
  end

  private

  def chart_type
    horizontal ? "horizontalBar" : "bar"
  end

  def chart_options
    super.deep_merge({
      indexAxis: horizontal ? "y" : "x",
      scales: {
        x: {
          display: true,
          title: {
            display: x_axis_label.present?,
            text: x_axis_label
          },
          grid: {
            display: show_grid
          },
          stacked: stacked
        },
        y: {
          display: true,
          title: {
            display: y_axis_label.present?,
            text: y_axis_label
          },
          grid: {
            display: show_grid
          },
          stacked: stacked
        }
      },
      plugins: chart_options[:plugins].merge({
        datalabels: {
          display: show_values,
          anchor: "end",
          align: "top",
          formatter: ->(value) { value.to_s }
        }
      })
    })
  end

  def formatted_data
    case data
    when Hash
      # Handle hash data with labels and datasets
      {
        labels: data[:labels] || data["labels"] || [],
        datasets: format_datasets(data[:datasets] || data["datasets"] || [])
      }
    when Array
      if data.first.is_a?(Hash)
        # Array of objects with label and value
        labels = data.map { |item| item[:label] || item["label"] || item[:name] || item["name"] }
        values = data.map { |item| item[:value] || item["value"] || item[:count] || item["count"] }

        {
          labels: labels,
          datasets: [{
            label: title || "Data",
            data: values,
            backgroundColor: generate_colors(values.length),
            borderColor: generate_border_colors(values.length),
            borderWidth: 1
          }]
        }
      else
        # Simple array of values
        labels = (1..data.length).map { |i| "Item #{i}" }
        {
          labels: labels,
          datasets: [{
            label: title || "Data",
            data: data,
            backgroundColor: generate_colors(data.length),
            borderColor: generate_border_colors(data.length),
            borderWidth: 1
          }]
        }
      end
    else
      {labels: [], datasets: []}
    end
  end

  def format_datasets(datasets)
    datasets.map.with_index do |dataset, index|
      color = colors[index % colors.length]
      {
        label: dataset[:label] || dataset["label"] || "Dataset #{index + 1}",
        data: dataset[:data] || dataset["data"] || [],
        backgroundColor: dataset[:backgroundColor] || dataset["backgroundColor"] || color,
        borderColor: dataset[:borderColor] || dataset["borderColor"] || darken_color(color),
        borderWidth: 1
      }
    end
  end

  def generate_colors(count)
    (0...count).map { |i| colors[i % colors.length] }
  end

  def generate_border_colors(count)
    (0...count).map { |i| darken_color(colors[i % colors.length]) }
  end

  def darken_color(hex_color)
    # Simple color darkening - in a real app you might want a more sophisticated approach
    hex_color.tr("#", "#").chars.each_slice(2).map do |pair|
      value = pair.join.to_i(16)
      [(value * 0.8).to_i, 0].max.to_s(16).rjust(2, "0")
    end.join.prepend("#")
  end
end

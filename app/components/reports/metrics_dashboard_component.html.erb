<div class="space-y-6" x-data="{ selectedPeriod: '<%= date_range %>' }">
  <!-- Period Selector -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
    <div class="flex items-center justify-between">
      <h2 class="text-lg font-semibold text-gray-900">Analytics Dashboard</h2>
      <div class="flex items-center space-x-2">
        <label class="text-sm font-medium text-gray-700">Period:</label>
        <select
          x-model="selectedPeriod"
          @change="window.location.href = updateUrlParam('date_range', selectedPeriod)"
          class="rounded-md border-gray-300 shadow-sm focus:border-zeiss-500 focus:ring-zeiss-500 text-sm"
        >
          <option value="7d" <%= 'selected' if date_range == '7d' %>>Last 7 Days</option>
          <option value="30d" <%= 'selected' if date_range == '30d' %>>Last 30 Days</option>
          <option value="90d" <%= 'selected' if date_range == '90d' %>>Last 90 Days</option>
          <option value="1y" <%= 'selected' if date_range == '1y' %>>Last Year</option>
        </select>
      </div>
    </div>
    <p class="text-sm text-gray-600 mt-1">
      Showing data for <%= metrics_data[:period_label] %>
      (<%= metrics_data[:start_date].strftime("%b %d, %Y") %> - <%= metrics_data[:end_date].strftime("%b %d, %Y") %>)
    </p>
          </div>

          <!-- Key Metrics Cards -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Sales Metrics -->
            <%= render Dashboard::StatCardComponent.new(
      title: "Total Sales",
      value: metrics_data[:sales_metrics][:total_sales],
      icon_name: "chart-line-up",
      color: "blue",
      subtitle: "#{metrics_data[:sales_metrics][:approved_sales]} approved"
    ) %>

            <%= render Dashboard::StatCardComponent.new(
      title: "Points Earned",
      value: metrics_data[:sales_metrics][:total_points],
      icon_name: "circle-check",
      color: "yellow",
      subtitle: "Avg: #{metrics_data[:sales_metrics][:average_points]}"
    ) %>

            <!-- Order Metrics -->
            <%= render Dashboard::StatCardComponent.new(
      title: "Total Orders",
      value: metrics_data[:order_metrics][:total_orders],
      icon_name: "cart-shopping",
      color: "green",
      subtitle: "#{metrics_data[:order_metrics][:approved_orders]} approved"
    ) %>

            <% if metrics_data[:user_metrics].any? %>
              <%= render Dashboard::StatCardComponent.new(
        title: "Active Users",
        value: metrics_data[:user_metrics][:active_users],
        icon_name: "users",
        color: "purple",
        subtitle: "#{metrics_data[:user_metrics][:new_users]} new"
      ) %>
            <% elsif metrics_data[:performance_metrics][:rank] %>
              <%= render Dashboard::StatCardComponent.new(
        title: "Your Rank",
        value: "##{metrics_data[:performance_metrics][:rank]}",
        icon_name: "circle-check",
        color: "orange",
        subtitle: "#{metrics_data[:performance_metrics][:points_earned]} points"
      ) %>
            <% end %>
          </div>

          <!-- Charts Section -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Sales Trend Chart -->
            <% if metrics_data[:sales_metrics][:daily_trend].any? %>
              <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-1">Sales Trend</h3>
                <p class="text-sm text-gray-600 mb-4">Daily sales over <%= metrics_data[:period_label].downcase %></p>
                <%= line_chart metrics_data[:sales_metrics][:daily_trend],
                    height: "300px",
                    colors: ["#3b82f6"],
                    curve: true,
                    suffix: " sales" %>
              </div>
            <% end %>

            <!-- Sales Status Breakdown -->
            <% if metrics_data[:sales_metrics][:status_breakdown].any? %>
              <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-1">Sales by Status</h3>
                <p class="text-sm text-gray-600 mb-4">Breakdown of sales statuses</p>
                <%= column_chart metrics_data[:sales_metrics][:status_breakdown].transform_keys(&:humanize),
                    height: "300px",
                    colors: ["#10b981", "#f59e0b", "#ef4444", "#6b7280"],
                    suffix: " sales" %>
              </div>
            <% end %>
          </div>

          <!-- Performance Section -->
          <% if metrics_data[:performance_metrics].any? %>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <!-- Top Performers (for admins) -->
              <% if metrics_data[:performance_metrics][:top_performers] %>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Performers</h3>
                  <div class="space-y-3">
                    <% metrics_data[:performance_metrics][:top_performers].each_with_index do |performer, index| %>
                      <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                          <span class="flex items-center justify-center w-8 h-8 bg-zeiss-500 text-white rounded-full text-sm font-medium mr-3">
                            <%= index + 1 %>
                          </span>
                          <span class="font-medium text-gray-900">User #<%= performer[:user][0] %></span>
                        </div>
                        <span class="text-sm font-medium text-zeiss-600"><%= performer[:points] %> pts</span>
                      </div>
                    <% end %>
                  </div>
                </div>
              <% end %>

              <!-- Regional Performance (for admins) -->
              <% if metrics_data[:performance_metrics][:regional_performance] %>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h3 class="text-lg font-semibold text-gray-900 mb-1">Regional Performance</h3>
                  <p class="text-sm text-gray-600 mb-4">Points earned by region</p>
                  <%= bar_chart metrics_data[:performance_metrics][:regional_performance],
                      height: "300px",
                      suffix: " points" %>
                </div>
              <% end %>

              <!-- Goal Progress (for regular users) -->
              <% if metrics_data[:performance_metrics][:goal_progress] %>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h3 class="text-lg font-semibold text-gray-900 mb-4">Goal Progress</h3>
                  <div class="space-y-4">
                    <div>
                      <div class="flex justify-between text-sm font-medium text-gray-900 mb-2">
                        <span>Monthly Sales Goal</span>
                        <span><%= metrics_data[:performance_metrics][:goal_progress] %>%</span>
                      </div>
                      <div class="w-full bg-gray-200 rounded-full h-2">
                        <div
                  class="bg-zeiss-500 h-2 rounded-full transition-all duration-300"
                  style="width: <%= metrics_data[:performance_metrics][:goal_progress] %>%"
                ></div>
                      </div>
                    </div>
                    <p class="text-sm text-gray-600">
                      You've completed <%= metrics_data[:performance_metrics][:sales_count] %> sales this period.
                    </p>
                  </div>
                </div>
              <% end %>
            </div>
          <% end %>
        </div>

        <script>
          function updateUrlParam(param, value) {
            const url = new URL(window.location);
            url.searchParams.set(param, value);
            return url.toString();
          }
        </script>

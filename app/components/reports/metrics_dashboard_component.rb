class Reports::MetricsDashboardComponent < ApplicationComponent
  option :user, type: Types::Any
  option :date_range, type: Types::String, default: proc { "30d" } # 7d, 30d, 90d, 1y
  option :refresh_interval, type: Types::Integer, default: proc { 60000 } # 1 minute

  private

  def metrics_data
    @metrics_data ||= calculate_metrics
  end

  def calculate_metrics
    case date_range
    when "7d"
      start_date = 7.days.ago
      period_label = "Last 7 Days"
    when "30d"
      start_date = 30.days.ago
      period_label = "Last 30 Days"
    when "90d"
      start_date = 90.days.ago
      period_label = "Last 90 Days"
    when "1y"
      start_date = 1.year.ago
      period_label = "Last Year"
    else
      start_date = 30.days.ago
      period_label = "Last 30 Days"
    end

    end_date = Date.current

    {
      period_label: period_label,
      start_date: start_date,
      end_date: end_date,
      sales_metrics: calculate_sales_metrics(start_date, end_date),
      user_metrics: calculate_user_metrics(start_date, end_date),
      order_metrics: calculate_order_metrics(start_date, end_date),
      performance_metrics: calculate_performance_metrics(start_date, end_date)
    }
  end

  def calculate_sales_metrics(start_date, end_date)
    scope = user_scope(Sale.where(created_at: start_date..end_date))

    {
      total_sales: scope.count,
      approved_sales: scope.approved.count,
      pending_sales: scope.pending.count,
      declined_sales: scope.declined.count,
      total_points: scope.approved.sum(:points),
      average_points: scope.approved.average(:points)&.round(2) || 0,
      daily_trend: daily_sales_trend(start_date, end_date),
      status_breakdown: scope.group(:status).count
    }
  end

  def calculate_user_metrics(start_date, end_date)
    if user&.super_admin? || user&.admin?
      {
        total_users: User.count,
        active_users: User.where(last_sign_in_at: start_date..end_date).count,
        new_users: User.where(created_at: start_date..end_date).count,
        user_growth: user_growth_trend(start_date, end_date)
      }
    else
      {}
    end
  end

  def calculate_order_metrics(start_date, end_date)
    scope = user_scope(Order.where(created_at: start_date..end_date))

    # Calculate total value manually since total_value is a method, not a column
    total_value = scope.sum(&:total_value)
    order_count = scope.count
    average_value = (order_count > 0) ? (total_value.to_f / order_count).round(2) : 0

    {
      total_orders: order_count,
      approved_orders: scope.approved.count,
      pending_orders: scope.pending.count,
      processed_orders: scope.processed.count,
      shipped_orders: scope.shipped.count,
      total_value: total_value,
      average_value: average_value,
      daily_trend: daily_orders_trend(start_date, end_date)
    }
  end

  def calculate_performance_metrics(start_date, end_date)
    if user&.regular_user?
      # Individual user performance
      {
        rank: user_rank,
        points_earned: user.sales.approved.where(created_at: start_date..end_date).sum(:points),
        sales_count: user.sales.where(created_at: start_date..end_date).count,
        goal_progress: calculate_goal_progress(start_date, end_date)
      }
    else
      # System-wide performance
      {
        top_performers: top_performers(start_date, end_date),
        regional_performance: regional_performance(start_date, end_date),
        brand_performance: brand_performance(start_date, end_date)
      }
    end
  end

  def daily_sales_trend(start_date, end_date)
    scope = user_scope(Sale.where(created_at: start_date..end_date))
    scope.group_by_day(:created_at).count
  end

  def daily_orders_trend(start_date, end_date)
    scope = user_scope(Order.where(created_at: start_date..end_date))
    scope.group_by_day(:created_at).count
  end

  def user_growth_trend(start_date, end_date)
    User.where(created_at: start_date..end_date).group_by_day(:created_at).count
  end

  def user_scope(base_scope)
    return base_scope if user&.super_admin?
    return base_scope.where(brand: user.admin_brand) if user&.brand_admin?
    return base_scope.where(region: user.region) if user&.region_admin?
    return base_scope.where(user: user) if user&.regular_user?

    base_scope
  end

  def user_rank
    # Calculate user's rank based on points earned this period
    return nil unless user&.regular_user?

    user_points = user.sales.approved.where(created_at: metrics_data[:start_date]..metrics_data[:end_date]).sum(:points)
    users_with_more_points = User.joins(:sales)
      .where(sales: {status: :approved, created_at: metrics_data[:start_date]..metrics_data[:end_date]})
      .group("users.id")
      .having("SUM(sales.points) > ?", user_points)
      .count

    users_with_more_points.count + 1
  end

  def calculate_goal_progress(start_date, end_date)
    return 0 unless user&.regular_user?

    # Assuming a monthly goal of 10 sales - this could be configurable
    monthly_goal = 10
    current_sales = user.sales.where(created_at: start_date..end_date).count

    [(current_sales.to_f / monthly_goal * 100).round(1), 100].min
  end

  def top_performers(start_date, end_date, limit = 5)
    Sale.approved
      .where(created_at: start_date..end_date)
      .joins(:user)
      .group("users.id", "users.first_name", "users.last_name")
      .sum(:points)
      .sort_by { |_, points| -points }
      .first(limit)
      .map { |user_data, points| {user: user_data, points: points} }
  end

  def regional_performance(start_date, end_date)
    Sale.approved
      .where(created_at: start_date..end_date)
      .joins(:region)
      .group("regions.name")
      .sum(:points)
  end

  def brand_performance(start_date, end_date)
    Sale.approved
      .where(created_at: start_date..end_date)
      .joins(:brand)
      .group("brands.name")
      .sum(:points)
  end
end

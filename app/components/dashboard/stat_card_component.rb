class Dashboard::StatCardComponent < ApplicationComponent
  option :title, type: Types::String
  option :value, type: Types::Any
  option :icon_name, type: Types::String
  option :color, type: Types::String, default: proc { "blue" }
  option :change, type: Types::String.optional, optional: true
  option :change_type, type: Types::String, default: proc { "neutral" } # positive, negative, neutral
  option :subtitle, type: Types::String.optional, optional: true
  option :link_to, type: Types::String.optional, optional: true
  option :trend_data, type: Types::Array, optional: true # Array of values for sparkline
  option :loading, type: Types::Bool, default: proc { false }

  private

  def color_classes
    case color
    when "blue"
      "bg-blue-500"
    when "green"
      "bg-green-500"
    when "yellow"
      "bg-yellow-500"
    when "red"
      "bg-red-500"
    when "purple"
      "bg-purple-500"
    when "indigo"
      "bg-indigo-500"
    when "orange"
      "bg-orange-500"
    else
      "bg-gray-500"
    end
  end

  def change_classes
    case change_type
    when "positive"
      "text-green-600"
    when "negative"
      "text-red-600"
    else
      "text-gray-500"
    end
  end

  def formatted_value
    case value
    when Numeric
      if value > 999_999
        "#{(value / 1_000_000.0).round(1)}M"
      elsif value > 999
        "#{(value / 1_000.0).round(1)}K"
      else
        number_with_delimiter(value)
      end
    else
      value.to_s
    end
  end

  def sparkline_id
    "sparkline-#{SecureRandom.hex(4)}"
  end
end

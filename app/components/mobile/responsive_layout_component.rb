class Mobile::ResponsiveLayoutComponent < ApplicationComponent
  option :title, type: Types::String
  option :show_back_button, type: Types::Bool, default: proc { true }
  option :back_url, type: Types::String, optional: true
  option :actions, type: Types::Array, default: proc { [] }
  option :tabs, type: Types::Array, default: proc { [] }
  option :current_tab, type: Types::String, optional: true

  private

  def mobile_header_classes
    "sticky top-0 z-40 bg-white border-b border-gray-200 px-4 py-3 sm:px-6 lg:hidden"
  end

  def desktop_header_classes
    "hidden lg:block bg-white border-b border-gray-200 px-6 py-4"
  end

  def mobile_content_classes
    "lg:hidden"
  end

  def desktop_content_classes
    "hidden lg:block"
  end

  def tab_classes(tab_key)
    base_classes = "flex-1 text-center py-3 px-2 text-sm font-medium border-b-2 transition-colors duration-200"

    if current_tab == tab_key
      "#{base_classes} border-zeiss-500 text-zeiss-600"
    else
      "#{base_classes} border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
    end
  end

  def action_button_classes(action)
    base_classes = "inline-flex items-center px-3 py-2 border text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500"

    case action[:style]
    when "primary"
      "#{base_classes} border-transparent text-white bg-zeiss-600 hover:bg-zeiss-700"
    when "secondary"
      "#{base_classes} border-gray-300 text-gray-700 bg-white hover:bg-gray-50"
    when "danger"
      "#{base_classes} border-transparent text-white bg-red-600 hover:bg-red-700"
    else
      "#{base_classes} border-gray-300 text-gray-700 bg-white hover:bg-gray-50"
    end
  end

  def mobile_action_classes
    "w-full justify-center mb-2 last:mb-0"
  end

  def desktop_action_classes
    "ml-3 first:ml-0"
  end
end

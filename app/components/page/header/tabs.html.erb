<div class="mt-3">
  <!-- Mobile Tab Select -->
  <div class="sm:hidden">
    <label class="sr-only" for="tabs">Select a tab</label>
    <select
      @change="
        const selectedIndex = Number($el.value);
        const tabButtons = $el.closest('[x-tabs]').querySelectorAll('[x-tabs\\:tab]');
        if (tabButtons[selectedIndex]) {
          tabButtons[selectedIndex].click();
        }
      "
      class="block w-full py-2 pl-3 pr-10 text-base border-gray-300 rounded-md focus:outline-hidden focus:ring-zeiss-500 focus:border-zeiss-500 sm:text-sm"
      id="tabs"
      name="tabs"
    >
      <template x-for="(tab, index) in tabs" :key="tab.id">
        <option :value="index" x-text="tab.title"></option>
      </template>
    </select>
  </div>

  <!-- Desktop Tab Navigation -->
  <div class="hidden sm:block">
    <div class="border-b border-gray-200">
      <!-- Alpine UI Tab List -->
      <nav x-tabs:list class="flex -mb-px space-x-4">
        <template x-for="(tab, index) in tabs" :key="tab.id">
          <button
            x-tabs:tab
            @click="onTabChange(index)"
            class="whitespace-nowrap pb-2 px-1 border-b-2 border-transparent font-medium text-sm"
            :class="$tab.isSelected ? activeClasses : inactiveClasses"
            :aria-current="$tab.isSelected"
          >
            <template x-if="tab.icon">
              <i :class="`fa-${tab.icon}`" class="mr-2 fa-fw fa-thin"></i>
            </template>
            <span x-text="tab.title"></span>
          </button>
        </template>
      </nav>
    </div>
  </div>
</div>

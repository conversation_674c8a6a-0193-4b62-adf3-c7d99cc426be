<div x-data="tabs(<%= (tabs.each_with_index.map{|t,i| {id:i,title:t.title}}).to_json %>, '<%= header.title.downcase %>_tab')" x-tabs class="py-6">
  <%= header %>
  <main class="my-8">
    <div class="max-w-6xl px-4 mx-auto sm:px-6 lg:px-8">
      <!-- Alpine UI Tab Panels -->
      <div x-tabs:panels>
        <% tabs.each_with_index do |tab, i| %>
          <div x-tabs:panel class="flex flex-col" x-cloak="<%= tab.cloak %>">
            <%= tab %>
          </div>
        <% end %>
      </div>
    </div>
  </main>
</div>

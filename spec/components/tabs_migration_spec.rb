require "rails_helper"

RSpec.describe "Tabs Migration to Alpine UI", type: :component do
  describe "Page::Tabbed::Component" do
    it "renders with Alpine UI directives" do
      component = Page::Tabbed::Component.new

      component.with_header(title: "Test Page", tabs: true)
      component.with_tab(title: "Tab 1") { "Content 1" }
      component.with_tab(title: "Tab 2") { "Content 2" }

      render_inline(component)

      # Check that Alpine UI directives are present
      expect(rendered_content).to have_selector("[x-tabs]")
      expect(rendered_content).to include("x-tabs:panels")
      expect(rendered_content).to include("x-tabs:panel")

      # Check that the tabs data is properly formatted
      expect(rendered_content).to have_selector("[x-data*='tabs']")

      # Check that tab content is rendered
      expect(rendered_content).to have_text("Content 1")
      expect(rendered_content).to have_text("Content 2")
    end
  end

  describe "Page::Header::Tabs" do
    it "renders with Alpine UI directives" do
      # Create a wrapper with tabs data to simulate the context
      wrapper_html = <<~HTML
        <div x-data="tabs([{id:0,title:'Tab 1'},{id:1,title:'Tab 2'}], 'test_tab')">
          #{render_inline(Page::Header::Tabs.new)}
        </div>
      HTML

      # Parse the HTML to check for Alpine UI elements
      expect(wrapper_html).to include("x-tabs:list")
      expect(wrapper_html).to include("x-tabs:tab")

      # Check that mobile select is present
      expect(rendered_content).to have_selector("select")
    end
  end

  describe "Mobile::ResponsiveLayoutComponent" do
    it "renders with Alpine UI directives" do
      component = Mobile::ResponsiveLayoutComponent.new(
        title: "Test Mobile Layout",
        tabs: [
          {key: "tab1", label: "Tab 1", icon: "home"},
          {key: "tab2", label: "Tab 2", icon: "user"}
        ],
        current_tab: "tab1"
      )

      render_inline(component) { "Test content" }

      # Check that Alpine UI directives are present
      expect(rendered_content).to have_selector("[x-tabs]")
      expect(rendered_content).to include("x-tabs:list")
      expect(rendered_content).to include("x-tabs:tab")
      expect(rendered_content).to include("x-tabs:panels")

      # Check that mobile reports data is present
      expect(rendered_content).to have_selector("[x-data*='mobileReports']")

      # Check that tab content is wrapped properly
      expect(rendered_content).to have_text("Test content")
    end

    it "maintains backward compatibility with data attributes" do
      component = Mobile::ResponsiveLayoutComponent.new(
        title: "Test",
        tabs: [{key: "test", label: "Test", icon: "home"}],
        current_tab: "test"
      )

      render_inline(component) do
        '<div data-tab-content="test">Test content</div>'.html_safe
      end

      # Check that data attributes are preserved in the content
      expect(rendered_content).to include('data-tab-content="test"')
      expect(rendered_content).to have_text("Test content")
    end
  end

  describe "Alpine UI integration" do
    it "includes Alpine UI plugin in application.js" do
      # Read the application.js file
      app_js_content = File.read(Rails.root.join("app/javascript/application.js"))

      # Check that Alpine UI is imported and configured
      expect(app_js_content).to include('import ui from "@alpinejs/ui"')
      expect(app_js_content).to include("Alpine.plugin(ui)")
    end

    it "maintains tabs.js for backward compatibility" do
      # Read the tabs.js file
      tabs_js_content = File.read(Rails.root.join("app/javascript/tabs.js"))

      # Check that it includes Alpine UI integration
      expect(tabs_js_content).to include("Alpine UI Tabs Implementation")
      expect(tabs_js_content).to include("onTabChange")
      expect(tabs_js_content).to include("activeClasses")
      expect(tabs_js_content).to include("inactiveClasses")
    end
  end
end
